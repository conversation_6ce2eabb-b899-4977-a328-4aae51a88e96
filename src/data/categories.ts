import { Category } from '@/types';

export const categories: Category[] = [
  {
    id: 'seyahat',
    name: 'Seyahat Ürünleri',
    description: 'Seyahatlerinizi kolaylaştıran pratik çözümler',
    icon: '🧳',
    subcategories: [
      {
        id: 'seyahat-aksesuarlari',
        name: 'Seyahat Aksesuarları',
        description: 'Valiz organizatörleri, seyahat yastıkları ve daha fazlası'
      },
      {
        id: 'pasaport-kutusu',
        name: 'Pasaport Kutusu',
        description: 'Pasaport ve önemli belgeleriniz için güvenli saklama'
      },
      {
        id: 'sabun-kutusu',
        name: '<PERSON><PERSON><PERSON>',
        description: 'Seyahat için pratik sabun ve kozmetik saklama çözümleri'
      },
      {
        id: 'valiz-organizatori',
        name: 'Valiz Organizatörü',
        description: 'Valizinizi düzenli tutmak için çeşitli boyutlarda organizatörler'
      }
    ]
  },
  {
    id: 'ev',
    name: '<PERSON><PERSON>',
    description: 'Evinizi daha düzenli ve pratik hale getirin',
    icon: '🏠',
    subcategories: [
      {
        id: 'banyo-aksesuarlari',
        name: 'Banyo Aksesuarları',
        description: 'Banyo düzeni için pratik çözümler'
      },
      {
        id: 'mutfak-aksesuarlari',
        name: 'Mutfak Aksesuarları',
        description: 'Mutfak organizasyonu için fonksiyonel ürünler'
      },
      {
        id: 'saklama-kaplari',
        name: 'Saklama Kapları',
        description: 'Çeşitli boyutlarda dayanıklı saklama çözümleri'
      },
      {
        id: 'temizlik-urunleri',
        name: 'Temizlik Ürünleri',
        description: 'Ev temizliği için pratik araçlar'
      }
    ]
  },
  {
    id: 'duzen',
    name: 'Düzen Ürünleri',
    description: 'Hayatınızı organize etmek için akıllı çözümler',
    icon: '📋',
    subcategories: [
      {
        id: 'duzenleyici-kutular',
        name: 'Düzenleyici Kutular',
        description: 'Çeşitli eşyalarınız için organize edici kutular'
      },
      {
        id: 'dosya-organizatori',
        name: 'Dosya Organizatörü',
        description: 'Belgelerinizi düzenli tutmak için çözümler'
      },
      {
        id: 'masa-duzenleyici',
        name: 'Masa Düzenleyici',
        description: 'Çalışma alanınızı organize etmek için ürünler'
      },
      {
        id: 'cekmece-organizatori',
        name: 'Çekmece Organizatörü',
        description: 'Çekmecelerinizi düzenli tutmak için bölmeli çözümler'
      }
    ]
  }
];

export const getCategoryById = (id: string) => {
  return categories.find(cat => cat.id === id);
};

export const getSubcategoryById = (categoryId: string, subcategoryId: string) => {
  const category = getCategoryById(categoryId);
  return category?.subcategories.find(sub => sub.id === subcategoryId);
};
