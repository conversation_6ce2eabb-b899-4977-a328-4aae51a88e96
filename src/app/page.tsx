'use client';

import { useState, useEffect } from 'react';
import Image from "next/image";
import Link from "next/link";
import { useCart } from "@/contexts/CartContext";
import { categories } from "@/data/categories";
import {
  ChevronLeft,
  ChevronRight,
  ShoppingBag,
  Heart,
  Star,
  Play,
  TrendingUp,
  Gift,
  Zap,
  Eye
} from "lucide-react";

export default function Home() {
  const { getTotalItems, addToCart } = useCart();
  const [currentSlide, setCurrentSlide] = useState(0);
  const [visibleProducts, setVisibleProducts] = useState(12);
  const [touchStart, setTouchStart] = useState(0);
  const [touchEnd, setTouchEnd] = useState(0);

  // Hero Slider Data
  const heroSlides = [
    {
      id: 1,
      title: "Seyahat Organizatörleri",
      subtitle: "Valizinizi düzenli tutun",
      image: "/assets/lilys-logo.jpg",
      buttonText: "<PERSON><PERSON><PERSON>",
      discount: "%30 İndirim",
      bgColor: "from-purple-500 to-pink-500"
    },
    {
      id: 2,
      title: "Mutfak Düzen Setleri",
      subtitle: "Mutfağınızı organize edin",
      image: "/assets/lilys-logo.jpg",
      buttonText: "Keşfet",
      discount: "Ücretsiz Kargo",
      bgColor: "from-blue-500 to-teal-500"
    },
    {
      id: 3,
      title: "Çekmece Organizatörleri",
      subtitle: "Her şey yerli yerinde",
      image: "/assets/lilys-logo.jpg",
      buttonText: "Satın Al",
      discount: "2 Al 1 Öde",
      bgColor: "from-green-500 to-emerald-500"
    }
  ];

  // Campaign Data
  const campaigns = [
    {
      id: 1,
      title: "Flash Sale",
      subtitle: "24 Saat Özel",
      discount: "%50",
      image: "/assets/lilys-logo.jpg",
      color: "from-red-500 to-pink-500"
    },
    {
      id: 2,
      title: "Yeni Üyelere Özel",
      subtitle: "İlk Alışveriş",
      discount: "%25",
      image: "/assets/lilys-logo.jpg",
      color: "from-blue-500 to-purple-500"
    },
    {
      id: 3,
      title: "Ücretsiz Kargo",
      subtitle: "500₺ Üzeri",
      discount: "0₺",
      image: "/assets/lilys-logo.jpg",
      color: "from-green-500 to-teal-500"
    }
  ];

  // Mock Products Data
  const allProducts = [
    {
      id: '1',
      name: 'Premium Seyahat Organizatörü Seti',
      description: 'Valizinizi düzenli tutmak için mükemmel çözüm',
      price: 299,
      originalPrice: 399,
      images: ['/assets/lilys-logo.jpg'],
      category: 'seyahat' as const,
      inStock: true,
      stockQuantity: 10,
      features: ['Su geçirmez', 'Dayanıklı malzeme', 'Çok bölmeli'],
      materials: ['Plastik'],
      colors: ['Pembe', 'Mavi'],
      tags: ['seyahat', 'organizatör'],
      rating: 4.8,
      reviewCount: 124,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: true,
      isNew: true,
      isTrending: true
    },
    {
      id: '2',
      name: 'Mutfak Düzen Seti',
      description: 'Mutfağınızı organize etmek için ideal set',
      price: 199,
      originalPrice: 249,
      images: ['/assets/lilys-logo.jpg'],
      category: 'ev' as const,
      inStock: true,
      stockQuantity: 15,
      features: ['BPA free', 'Bulaşık makinesi uyumlu'],
      materials: ['Plastik'],
      colors: ['Beyaz', 'Gri'],
      tags: ['mutfak', 'düzen'],
      rating: 4.9,
      reviewCount: 89,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: true,
      isNew: false,
      isTrending: true
    },
    {
      id: '3',
      name: 'Çekmece Organizatör Seti',
      description: 'Çekmecelerinizi düzenli tutmak için çözüm',
      price: 149,
      images: ['/assets/lilys-logo.jpg'],
      category: 'duzen' as const,
      inStock: true,
      stockQuantity: 20,
      features: ['Ayarlanabilir bölmeler', 'Kaymaz taban'],
      materials: ['Plastik'],
      colors: ['Şeffaf', 'Pembe'],
      tags: ['çekmece', 'organizatör'],
      rating: 4.7,
      reviewCount: 156,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: true,
      isNew: true,
      isTrending: false
    },
    // Daha fazla ürün ekleyelim
    ...Array.from({ length: 20 }, (_, i) => ({
      id: `${i + 4}`,
      name: `Ürün ${i + 4}`,
      description: 'Harika bir ürün açıklaması',
      price: Math.floor(Math.random() * 300) + 50,
      originalPrice: Math.floor(Math.random() * 400) + 100,
      images: ['/assets/lilys-logo.jpg'],
      category: ['seyahat', 'ev', 'duzen'][Math.floor(Math.random() * 3)] as any,
      inStock: true,
      stockQuantity: Math.floor(Math.random() * 50) + 1,
      features: ['Özellik 1', 'Özellik 2'],
      materials: ['Plastik'],
      colors: ['Renk 1', 'Renk 2'],
      tags: ['tag1', 'tag2'],
      rating: 4 + Math.random(),
      reviewCount: Math.floor(Math.random() * 200) + 10,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: Math.random() > 0.7,
      isNew: Math.random() > 0.8,
      isTrending: Math.random() > 0.6
    }))
  ];

  const trendingProducts = allProducts.filter(p => p.isTrending);
  const featuredProducts = allProducts.filter(p => p.isFeatured);

  // Touch handlers for mobile swipe
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchEnd(0);
    setTouchStart(e.targetTouches[0].clientX);
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  };

  const handleTouchEnd = () => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;

    if (isLeftSwipe) {
      setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    }
    if (isRightSwipe) {
      setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
    }
  };

  // Auto slide (only on desktop)
  useEffect(() => {
    const timer = setInterval(() => {
      if (window.innerWidth >= 768) { // Only auto-slide on desktop
        setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
      }
    }, 5000);
    return () => clearInterval(timer);
  }, [heroSlides.length]);

  const loadMoreProducts = () => {
    setVisibleProducts(prev => Math.min(prev + 12, allProducts.length));
  };

  // Infinite scroll effect
  useEffect(() => {
    const handleScroll = () => {
      if (window.innerHeight + document.documentElement.scrollTop >= document.documentElement.offsetHeight - 1000) {
        if (visibleProducts < allProducts.length) {
          loadMoreProducts();
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [visibleProducts, allProducts.length]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/assets/lilys-logo.jpg"
                alt="Lilys Istanbul"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="text-xl font-bold text-gray-900">Lilys İstanbul</span>
            </Link>

            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/links" className="text-gray-700 hover:text-[#B8860B] transition-colors">Linkler</Link>
              <Link href="/admin" className="text-gray-700 hover:text-[#B8860B] transition-colors">Admin</Link>
            </nav>

            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-700 hover:text-[#B8860B] transition-colors">
                <Heart className="w-6 h-6" />
              </button>
              <Link href="/cart" className="p-2 text-gray-700 hover:text-[#B8860B] transition-colors relative">
                <ShoppingBag className="w-6 h-6" />
                <span className="absolute -top-1 -right-1 bg-[#B8860B] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {getTotalItems()}
                </span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Slider - Trendyol Style */}
      <section className="py-4 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          {/* Desktop: 3 slides side by side */}
          <div className="hidden md:grid md:grid-cols-3 gap-4">
            {heroSlides.map((slide, index) => (
              <div
                key={slide.id}
                className={`relative aspect-[3/4] bg-gradient-to-br ${slide.bgColor} rounded-2xl overflow-hidden group cursor-pointer hover:scale-105 transition-transform duration-300`}
              >
                <div className="absolute inset-0 bg-black/10"></div>
                <div className="relative z-10 p-6 h-full flex flex-col justify-between">
                  <div>
                    <div className="inline-block bg-[#B8860B] text-white px-3 py-1 rounded-full text-sm font-semibold mb-4">
                      {slide.discount}
                    </div>
                    <h2 className="text-2xl font-bold text-white mb-3 leading-tight">{slide.title}</h2>
                    <p className="text-white/90 text-sm mb-6">{slide.subtitle}</p>
                  </div>
                  <div className="flex items-end justify-between">
                    <button className="bg-white text-[#B8860B] font-bold py-3 px-6 rounded-full hover:bg-gray-100 transition-colors">
                      {slide.buttonText}
                    </button>
                    <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                      <Image
                        src={slide.image}
                        alt={slide.title}
                        width={60}
                        height={60}
                        className="rounded-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Mobile: Swipeable slider */}
          <div className="md:hidden relative">
            <div
              className="overflow-hidden rounded-2xl"
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
            >
              <div
                className="flex transition-transform duration-300 ease-out"
                style={{ transform: `translateX(-${currentSlide * 100}%)` }}
              >
                {heroSlides.map((slide, index) => (
                  <div
                    key={slide.id}
                    className={`w-full flex-shrink-0 aspect-[3/4] bg-gradient-to-br ${slide.bgColor} relative`}
                  >
                    <div className="absolute inset-0 bg-black/10"></div>
                    <div className="relative z-10 p-6 h-full flex flex-col justify-between">
                      <div>
                        <div className="inline-block bg-[#B8860B] text-white px-3 py-1 rounded-full text-sm font-semibold mb-4">
                          {slide.discount}
                        </div>
                        <h2 className="text-3xl font-bold text-white mb-4 leading-tight">{slide.title}</h2>
                        <p className="text-white/90 mb-8">{slide.subtitle}</p>
                      </div>
                      <div className="flex items-end justify-between">
                        <button className="bg-white text-[#B8860B] font-bold py-3 px-6 rounded-full hover:bg-gray-100 transition-colors">
                          {slide.buttonText}
                        </button>
                        <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                          <Image
                            src={slide.image}
                            alt={slide.title}
                            width={80}
                            height={80}
                            className="rounded-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Navigation Dots */}
            <div className="flex justify-center mt-4 space-x-2">
              {heroSlides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-2 h-2 rounded-full transition-colors ${
                    index === currentSlide ? 'bg-[#B8860B]' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>

            {/* Mobile Swipe Controls */}
            <button
              onClick={() => setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length)}
              className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg transition-colors"
            >
              <ChevronLeft className="w-5 h-5" />
            </button>
            <button
              onClick={() => setCurrentSlide((prev) => (prev + 1) % heroSlides.length)}
              className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full shadow-lg transition-colors"
            >
              <ChevronRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </section>

      {/* Campaigns */}
      <section className="py-8 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900 flex items-center">
              <Gift className="w-6 h-6 mr-2 text-[#B8860B]" />
              Kampanyalar
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {campaigns.map((campaign) => (
              <div
                key={campaign.id}
                className={`relative bg-gradient-to-r ${campaign.color} rounded-2xl p-6 text-white overflow-hidden cursor-pointer hover:scale-105 transition-transform`}
              >
                <div className="relative z-10">
                  <h3 className="text-xl font-bold mb-2">{campaign.title}</h3>
                  <p className="text-white/90 mb-4">{campaign.subtitle}</p>
                  <div className="text-3xl font-bold">{campaign.discount}</div>
                </div>
                <div className="absolute -right-4 -bottom-4 opacity-20">
                  <Image
                    src={campaign.image}
                    alt={campaign.title}
                    width={100}
                    height={100}
                    className="rounded-full"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Trending Products */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 flex items-center">
              <TrendingUp className="w-7 h-7 mr-3 text-[#B8860B]" />
              Trend Ürünler
            </h2>
            <Link href="/#all-products" className="text-[#B8860B] hover:text-[#A0750A] font-semibold flex items-center">
              Tümünü Gör
              <ChevronRight className="w-4 h-4 ml-1" />
            </Link>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {trendingProducts.slice(0, 6).map((product) => (
              <Link
                key={product.id}
                href={`/product/${product.id}`}
                className="group bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
              >
                <div className="relative">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    width={200}
                    height={200}
                    className="w-full h-40 object-cover"
                  />
                  {product.isNew && (
                    <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                      YENİ
                    </span>
                  )}
                  <button className="absolute top-2 right-2 p-1.5 bg-white/80 rounded-full hover:bg-white transition-colors">
                    <Heart className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
                <div className="p-3">
                  <h3 className="font-semibold text-sm text-gray-900 mb-1 line-clamp-2">{product.name}</h3>
                  <div className="flex items-center mb-2">
                    <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    <span className="text-xs text-gray-600 ml-1">{product.rating.toFixed(1)}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-lg font-bold text-[#B8860B]">{product.price}₺</span>
                      {product.originalPrice && (
                        <span className="text-xs text-gray-500 line-through ml-1">{product.originalPrice}₺</span>
                      )}
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Videos Section */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 flex items-center">
              <Play className="w-7 h-7 mr-3 text-[#B8860B]" />
              Ürün Videoları
            </h2>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((video) => (
              <div
                key={video}
                className="relative bg-gray-100 rounded-2xl overflow-hidden aspect-video cursor-pointer group hover:scale-105 transition-transform"
              >
                <Image
                  src="/assets/lilys-logo.jpg"
                  alt={`Video ${video}`}
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-black/30 flex items-center justify-center">
                  <div className="bg-white/90 rounded-full p-4 group-hover:bg-white transition-colors">
                    <Play className="w-8 h-8 text-[#B8860B]" />
                  </div>
                </div>
                <div className="absolute bottom-4 left-4 text-white">
                  <h3 className="font-semibold">Ürün Tanıtım Videosu {video}</h3>
                  <p className="text-sm text-white/80">2:30</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Discover Products */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between mb-8">
            <h2 className="text-3xl font-bold text-gray-900 flex items-center">
              <Eye className="w-7 h-7 mr-3 text-[#B8860B]" />
              Ürünleri Keşfet
            </h2>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
            {featuredProducts.slice(0, 8).map((product) => (
              <Link
                key={product.id}
                href={`/product/${product.id}`}
                className="group bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
              >
                <div className="relative">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    width={300}
                    height={250}
                    className="w-full h-48 object-cover"
                  />
                  {product.originalPrice && (
                    <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                      %{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)} İndirim
                    </span>
                  )}
                  <button className="absolute top-2 right-2 p-2 bg-white/80 rounded-full hover:bg-white transition-colors">
                    <Heart className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{product.name}</h3>
                  <div className="flex items-center mb-3">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{product.rating.toFixed(1)}</span>
                    </div>
                    <span className="text-sm text-gray-500 ml-2">({product.reviewCount})</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <span className="text-xl font-bold text-[#B8860B]">{product.price}₺</span>
                      {product.originalPrice && (
                        <span className="text-sm text-gray-500 line-through ml-2">{product.originalPrice}₺</span>
                      )}
                    </div>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        addToCart(product);
                      }}
                      className="bg-[#F4C2C2] hover:bg-[#E8A5A5] text-gray-900 font-semibold py-1 px-3 rounded-full text-sm transition-colors"
                    >
                      Sepete Ekle
                    </button>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Categories */}
      <section className="py-12 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Kategoriler</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              İhtiyacınıza uygun ürünleri kolayca bulun
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {categories.map((category) => (
              <Link
                key={category.id}
                href={`/category/${category.id}`}
                className="group relative bg-gradient-to-br from-[#F4C2C2] to-[#E8A5A5] rounded-3xl p-8 text-center hover:shadow-2xl transition-all duration-300 hover:scale-105 overflow-hidden"
              >
                <div className="absolute inset-0 bg-white/10 opacity-0 group-hover:opacity-100 transition-opacity"></div>
                <div className="relative z-10">
                  <div className="text-6xl mb-6">{category.icon}</div>
                  <h3 className="text-2xl font-bold text-white mb-4">{category.name}</h3>
                  <p className="text-white/90 mb-6">{category.description}</p>
                  <div className="inline-flex items-center bg-white/20 backdrop-blur-sm text-white font-semibold py-2 px-6 rounded-full group-hover:bg-white/30 transition-colors">
                    Keşfet
                    <ChevronRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* All Products with Infinite Scroll */}
      <section id="all-products" className="py-12 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Tüm Ürünler</h2>
            <p className="text-gray-600">
              {allProducts.length} ürün arasından seçim yapın
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {allProducts.slice(0, visibleProducts).map((product) => (
              <Link
                key={product.id}
                href={`/product/${product.id}`}
                className="group bg-white rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-300 hover:scale-105"
              >
                <div className="relative">
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    width={200}
                    height={200}
                    className="w-full h-40 object-cover"
                  />
                  {product.originalPrice && (
                    <span className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                      %{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}
                    </span>
                  )}
                  {product.isNew && (
                    <span className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-xs font-semibold">
                      YENİ
                    </span>
                  )}
                  <button className="absolute bottom-2 right-2 p-1.5 bg-white/80 rounded-full hover:bg-white transition-colors opacity-0 group-hover:opacity-100">
                    <Heart className="w-4 h-4 text-gray-600" />
                  </button>
                </div>
                <div className="p-3">
                  <h3 className="font-semibold text-sm text-gray-900 mb-1 line-clamp-2">{product.name}</h3>
                  <div className="flex items-center mb-2">
                    <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    <span className="text-xs text-gray-600 ml-1">{product.rating.toFixed(1)}</span>
                    <span className="text-xs text-gray-500 ml-1">({product.reviewCount})</span>
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-lg font-bold text-[#B8860B]">{product.price}₺</span>
                      {product.originalPrice && (
                        <span className="text-xs text-gray-500 line-through">{product.originalPrice}₺</span>
                      )}
                    </div>
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        addToCart(product);
                      }}
                      className="w-full bg-[#F4C2C2] hover:bg-[#E8A5A5] text-gray-900 font-semibold py-1.5 px-2 rounded-lg text-xs transition-colors"
                    >
                      Sepete Ekle
                    </button>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Load More Button */}
          {visibleProducts < allProducts.length && (
            <div className="text-center mt-12">
              <button
                onClick={loadMoreProducts}
                className="bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 hover:scale-105 shadow-lg"
              >
                Daha Fazla Ürün Yükle ({allProducts.length - visibleProducts} ürün kaldı)
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-3 mb-6">
                <Image
                  src="/assets/lilys-logo.jpg"
                  alt="Lilys Istanbul"
                  width={60}
                  height={60}
                  className="rounded-full"
                />
                <div>
                  <h3 className="text-2xl font-bold">Lilys İstanbul</h3>
                  <p className="text-gray-400">Sağlıklı ve Dayanıklı Özel Plastik Ürünler</p>
                </div>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                Kadınlara özel tasarlanmış seyahat, ev ve düzen ürünleri ile hayatınızı daha pratik hale getirin.
              </p>
              <div className="flex space-x-4">
                <Link href="/links" className="bg-[#B8860B] hover:bg-[#A0750A] text-white px-4 py-2 rounded-lg transition-colors">
                  Sosyal Medya
                </Link>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Kategoriler</h4>
              <ul className="space-y-2">
                {categories.map((category) => (
                  <li key={category.id}>
                    <Link href={`/category/${category.id}`} className="text-gray-400 hover:text-white transition-colors">
                      {category.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Hızlı Linkler</h4>
              <ul className="space-y-2">
                <li><Link href="/links" className="text-gray-400 hover:text-white transition-colors">Sosyal Medya</Link></li>
                <li><Link href="/cart" className="text-gray-400 hover:text-white transition-colors">Sepetim</Link></li>
                <li><Link href="/admin" className="text-gray-400 hover:text-white transition-colors">Admin</Link></li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-8 pt-8 text-center">
            <p className="text-gray-400">
              © {new Date().getFullYear()} Lilys İstanbul. Tüm hakları saklıdır.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
