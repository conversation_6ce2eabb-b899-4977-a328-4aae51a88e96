'use client';

import { useState } from 'react';
import Image from "next/image";
import Link from "next/link";
import { 
  BarChart3, 
  Package, 
  ShoppingCart, 
  Users, 
  TrendingUp, 
  TrendingDown,
  Eye,
  Edit,
  Trash2,
  Plus
} from "lucide-react";

export default function AdminPage() {
  const [activeTab, setActiveTab] = useState('dashboard');

  // Mock data - gerçek veriler Firebase'den gelecek
  const stats = {
    totalOrders: 156,
    totalRevenue: 45680,
    totalProducts: 24,
    totalCustomers: 89,
    monthlyGrowth: 12.5
  };

  const recentOrders = [
    {
      id: '#LI001234',
      customer: 'Ayşe Yılmaz',
      amount: 299,
      status: 'Hazırlanıyor',
      date: '2024-01-15'
    },
    {
      id: '#LI001235',
      customer: 'Fatma Kaya',
      amount: 199,
      status: 'Kargoda',
      date: '2024-01-14'
    },
    {
      id: '#LI001236',
      customer: 'Zeynep Demir',
      amount: 149,
      status: 'Teslim Edildi',
      date: '2024-01-13'
    }
  ];

  const products = [
    {
      id: '1',
      name: 'Premium Seyahat Organizatörü Seti',
      price: 299,
      stock: 15,
      category: 'Seyahat',
      status: 'Aktif',
      image: '/assets/lilys-logo.jpg'
    },
    {
      id: '2',
      name: 'Mutfak Düzen Seti',
      price: 199,
      stock: 8,
      category: 'Ev',
      status: 'Aktif',
      image: '/assets/lilys-logo.jpg'
    },
    {
      id: '3',
      name: 'Çekmece Organizatör Seti',
      price: 149,
      stock: 0,
      category: 'Düzen',
      status: 'Stokta Yok',
      image: '/assets/lilys-logo.jpg'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hazırlanıyor': return 'bg-yellow-100 text-yellow-800';
      case 'Kargoda': return 'bg-blue-100 text-blue-800';
      case 'Teslim Edildi': return 'bg-green-100 text-green-800';
      case 'Aktif': return 'bg-green-100 text-green-800';
      case 'Stokta Yok': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2]">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-[#F4C2C2]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-3">
              <Image
                src="/assets/lilys-logo.jpg"
                alt="Lilys Istanbul"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="text-xl font-bold text-gray-900">Lilys İstanbul Admin</span>
            </div>
            
            <div className="flex items-center space-x-4">
              <Link href="/" className="text-[#B8860B] hover:text-[#A0750A] transition-colors">
                Siteyi Görüntüle
              </Link>
              <div className="w-8 h-8 bg-[#B8860B] rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-semibold">A</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar */}
          <div className="lg:w-64">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <nav className="space-y-2">
                {[
                  { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
                  { id: 'products', label: 'Ürünler', icon: Package },
                  { id: 'orders', label: 'Siparişler', icon: ShoppingCart },
                  { id: 'customers', label: 'Müşteriler', icon: Users }
                ].map((item) => (
                  <button
                    key={item.id}
                    onClick={() => setActiveTab(item.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                      activeTab === item.id
                        ? 'bg-[#B8860B] text-white'
                        : 'text-gray-700 hover:bg-[#F4C2C2]/30'
                    }`}
                  >
                    <item.icon className="w-5 h-5" />
                    <span className="font-semibold">{item.label}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            {activeTab === 'dashboard' && (
              <div className="space-y-8">
                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Toplam Sipariş</p>
                        <p className="text-3xl font-bold text-gray-900">{stats.totalOrders}</p>
                      </div>
                      <ShoppingCart className="w-8 h-8 text-[#B8860B]" />
                    </div>
                    <div className="flex items-center mt-4">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-500">+{stats.monthlyGrowth}%</span>
                    </div>
                  </div>

                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Toplam Gelir</p>
                        <p className="text-3xl font-bold text-gray-900">{stats.totalRevenue.toLocaleString()}₺</p>
                      </div>
                      <BarChart3 className="w-8 h-8 text-[#B8860B]" />
                    </div>
                    <div className="flex items-center mt-4">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-500">+{stats.monthlyGrowth}%</span>
                    </div>
                  </div>

                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Toplam Ürün</p>
                        <p className="text-3xl font-bold text-gray-900">{stats.totalProducts}</p>
                      </div>
                      <Package className="w-8 h-8 text-[#B8860B]" />
                    </div>
                    <div className="flex items-center mt-4">
                      <span className="text-sm text-gray-500">Aktif ürünler</span>
                    </div>
                  </div>

                  <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-600">Toplam Müşteri</p>
                        <p className="text-3xl font-bold text-gray-900">{stats.totalCustomers}</p>
                      </div>
                      <Users className="w-8 h-8 text-[#B8860B]" />
                    </div>
                    <div className="flex items-center mt-4">
                      <TrendingUp className="w-4 h-4 text-green-500 mr-1" />
                      <span className="text-sm text-green-500">+8 bu ay</span>
                    </div>
                  </div>
                </div>

                {/* Recent Orders */}
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">Son Siparişler</h3>
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Sipariş No</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Müşteri</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Tutar</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Durum</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Tarih</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentOrders.map((order) => (
                          <tr key={order.id} className="border-b border-gray-100">
                            <td className="py-3 px-4 font-semibold text-[#B8860B]">{order.id}</td>
                            <td className="py-3 px-4">{order.customer}</td>
                            <td className="py-3 px-4 font-semibold">{order.amount}₺</td>
                            <td className="py-3 px-4">
                              <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(order.status)}`}>
                                {order.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-gray-600">{order.date}</td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'products' && (
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-bold text-gray-900">Ürün Yönetimi</h2>
                  <button className="bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-2 px-4 rounded-lg flex items-center space-x-2 transition-colors">
                    <Plus className="w-4 h-4" />
                    <span>Yeni Ürün</span>
                  </button>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Ürün</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Kategori</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Fiyat</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Stok</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Durum</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">İşlemler</th>
                        </tr>
                      </thead>
                      <tbody>
                        {products.map((product) => (
                          <tr key={product.id} className="border-b border-gray-100">
                            <td className="py-3 px-4">
                              <div className="flex items-center space-x-3">
                                <Image
                                  src={product.image}
                                  alt={product.name}
                                  width={40}
                                  height={40}
                                  className="rounded-lg object-cover"
                                />
                                <span className="font-semibold">{product.name}</span>
                              </div>
                            </td>
                            <td className="py-3 px-4">{product.category}</td>
                            <td className="py-3 px-4 font-semibold">{product.price}₺</td>
                            <td className="py-3 px-4">{product.stock}</td>
                            <td className="py-3 px-4">
                              <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(product.status)}`}>
                                {product.status}
                              </span>
                            </td>
                            <td className="py-3 px-4">
                              <div className="flex items-center space-x-2">
                                <button className="p-1 text-gray-600 hover:text-[#B8860B] transition-colors">
                                  <Eye className="w-4 h-4" />
                                </button>
                                <button className="p-1 text-gray-600 hover:text-[#B8860B] transition-colors">
                                  <Edit className="w-4 h-4" />
                                </button>
                                <button className="p-1 text-gray-600 hover:text-red-500 transition-colors">
                                  <Trash2 className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'orders' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900">Sipariş Yönetimi</h2>
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-gray-200">
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Sipariş No</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Müşteri</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Tutar</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Durum</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">Tarih</th>
                          <th className="text-left py-3 px-4 font-semibold text-gray-700">İşlemler</th>
                        </tr>
                      </thead>
                      <tbody>
                        {recentOrders.map((order) => (
                          <tr key={order.id} className="border-b border-gray-100">
                            <td className="py-3 px-4 font-semibold text-[#B8860B]">{order.id}</td>
                            <td className="py-3 px-4">{order.customer}</td>
                            <td className="py-3 px-4 font-semibold">{order.amount}₺</td>
                            <td className="py-3 px-4">
                              <span className={`px-3 py-1 rounded-full text-xs font-semibold ${getStatusColor(order.status)}`}>
                                {order.status}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-gray-600">{order.date}</td>
                            <td className="py-3 px-4">
                              <div className="flex items-center space-x-2">
                                <button className="p-1 text-gray-600 hover:text-[#B8860B] transition-colors">
                                  <Eye className="w-4 h-4" />
                                </button>
                                <button className="p-1 text-gray-600 hover:text-[#B8860B] transition-colors">
                                  <Edit className="w-4 h-4" />
                                </button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'customers' && (
              <div className="space-y-6">
                <h2 className="text-2xl font-bold text-gray-900">Müşteri Yönetimi</h2>
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
                  <p className="text-gray-600">Müşteri listesi ve detayları burada görüntülenecek.</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
