import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { CartProvider } from "@/contexts/CartContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Lilys İstanbul - Sağlıklı ve Dayanıklı Plastik Ürünler",
  description: "Ka<PERSON><PERSON><PERSON><PERSON>a özel seyahat, ev ve düzen ürünleri. Sağlıklı ve dayanıklı plastik ürünlerle hayatınızı organize edin.",
  keywords: "seyahat organizatörü, ev düzeni, plastik ürünler, kadın aksesuarl<PERSON>ı, dü<PERSON>leyici kutular",
  icons: {
    icon: '/assets/lilys-logo.jpg',
    shortcut: '/assets/lilys-logo.jpg',
    apple: '/assets/lilys-logo.jpg',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="tr">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <CartProvider>
          {children}
        </CartProvider>
      </body>
    </html>
  );
}
