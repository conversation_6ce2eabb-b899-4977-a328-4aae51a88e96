'use client';

import { use } from 'react';
import Image from "next/image";
import Link from "next/link";
import { categories, getCategoryById } from "@/data/categories";
import { useCart } from "@/contexts/CartContext";
import { ArrowLeft, Filter, Grid, List, Star, Heart, ShoppingBag } from "lucide-react";
import { useState } from "react";
import { Metadata } from 'next';

interface CategoryPageProps {
  params: Promise<{ slug: string }>;
}

export default function CategoryPage({ params }: CategoryPageProps) {
  const { slug } = use(params);
  const category = getCategoryById(slug);
  const { getTotalItems, addToCart } = useCart();
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);

  // Mock products - gerçek veriler Firebase'den gelecek
  const mockProducts = [
    {
      id: '1',
      name: 'Premium Seyahat Organizatörü',
      description: 'Valizinizi düzenli tutmak için mükemmel çözüm',
      price: 199,
      originalPrice: 249,
      images: ['/assets/lilys-logo.jpg'],
      category: slug as any,
      inStock: true,
      stockQuantity: 10,
      features: ['Su geçirmez', 'Dayanıklı'],
      materials: ['Plastik'],
      colors: ['Pembe', 'Mavi', 'Gri'],
      tags: ['seyahat'],
      rating: 4.8,
      reviewCount: 45,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: true,
      isNew: true
    },
    {
      id: '2',
      name: 'Pasaport Koruyucu Kılıf',
      description: 'Pasaport ve önemli belgeleriniz için güvenli saklama',
      price: 89,
      images: ['/assets/lilys-logo.jpg'],
      category: slug as any,
      inStock: true,
      stockQuantity: 15,
      features: ['Su geçirmez', 'Kompakt'],
      materials: ['Plastik'],
      colors: ['Siyah', 'Kahverengi'],
      tags: ['pasaport'],
      rating: 4.9,
      reviewCount: 32,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: false,
      isNew: false
    },
    {
      id: '3',
      name: 'Seyahat Sabun Kutusu Seti',
      description: 'Sızdırmaz tasarım ile güvenli taşıma',
      price: 129,
      originalPrice: 159,
      images: ['/assets/lilys-logo.jpg'],
      category: slug as any,
      inStock: false,
      stockQuantity: 0,
      features: ['Sızdırmaz', 'Taşınabilir'],
      materials: ['Plastik'],
      colors: ['Şeffaf', 'Pembe'],
      tags: ['sabun'],
      rating: 4.7,
      reviewCount: 28,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      isFeatured: false,
      isNew: true
    }
  ];

  if (!category) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Kategori bulunamadı</h1>
          <Link href="/shop" className="text-[#B8860B] hover:underline">
            Mağazaya dön
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2]">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-[#F4C2C2]/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/assets/lilys-logo.jpg"
                alt="Lilys Istanbul"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="text-xl font-bold text-gray-900">Lilys İstanbul</span>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/" className="text-[#B8860B] font-semibold">Ana Sayfa</Link>
              <Link href="/links" className="text-gray-700 hover:text-[#B8860B] transition-colors">Linkler</Link>
            </nav>

            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-700 hover:text-[#B8860B] transition-colors">
                <Heart className="w-6 h-6" />
              </button>
              <Link href="/cart" className="p-2 text-gray-700 hover:text-[#B8860B] transition-colors relative">
                <ShoppingBag className="w-6 h-6" />
                <span className="absolute -top-1 -right-1 bg-[#B8860B] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {getTotalItems()}
                </span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="bg-white/50 py-4 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-[#B8860B] hover:underline">Ana Sayfa</Link>
            <span className="text-gray-500">/</span>
            <span className="text-gray-900 font-semibold">{category.name}</span>
          </div>
        </div>
      </div>

      {/* Category Header */}
      <section className="py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <div className="text-6xl mb-4">{category.icon}</div>
          <h1 className="text-4xl font-bold text-gray-900 mb-4">{category.name}</h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">{category.description}</p>
        </div>
      </section>

      {/* Subcategories */}
      <section className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap gap-3 justify-center">
            <button className="bg-[#B8860B] text-white px-6 py-2 rounded-full font-semibold">
              Tümü
            </button>
            {category.subcategories.map((sub) => (
              <button
                key={sub.id}
                className="bg-white/80 hover:bg-white text-gray-700 hover:text-[#B8860B] px-6 py-2 rounded-full font-semibold transition-colors border border-gray-200"
              >
                {sub.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Filters and View Controls */}
      <section className="py-4 px-4 sm:px-6 lg:px-8 bg-white/50">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center space-x-2 bg-white px-4 py-2 rounded-lg border border-gray-200 hover:border-[#B8860B] transition-colors"
              >
                <Filter className="w-4 h-4" />
                <span>Filtrele</span>
              </button>
              <span className="text-gray-600">{mockProducts.length} ürün</span>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-[#B8860B] text-white' : 'bg-white text-gray-600'}`}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-[#B8860B] text-white' : 'bg-white text-gray-600'}`}
              >
                <List className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className={`grid gap-6 ${viewMode === 'grid' ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1'}`}>
            {mockProducts.map((product) => (
              <div
                key={product.id}
                className={`group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-105 ${viewMode === 'list' ? 'flex' : ''}`}
              >
                <Link href={`/shop/product/${product.id}`} className={`relative block ${viewMode === 'list' ? 'w-48 flex-shrink-0' : ''}`}>
                  <Image
                    src={product.images[0]}
                    alt={product.name}
                    width={400}
                    height={300}
                    className={`w-full object-cover ${viewMode === 'list' ? 'h-48' : 'h-64'}`}
                  />
                  {product.isNew && (
                    <span className="absolute top-4 left-4 bg-[#B8860B] text-white px-3 py-1 rounded-full text-sm font-semibold">
                      Yeni
                    </span>
                  )}
                  {!product.inStock && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <span className="bg-red-500 text-white px-4 py-2 rounded-full font-semibold">
                        Stokta Yok
                      </span>
                    </div>
                  )}
                  <button className="absolute top-4 right-4 p-2 bg-white/80 rounded-full hover:bg-white transition-colors">
                    <Heart className="w-5 h-5 text-gray-600" />
                  </button>
                </Link>
                
                <div className="p-6 flex-1">
                  <Link href={`/shop/product/${product.id}`}>
                    <h3 className="text-xl font-bold text-gray-900 mb-2 hover:text-[#B8860B] transition-colors">{product.name}</h3>
                  </Link>
                  <p className="text-gray-600 mb-3">{product.description}</p>
                  
                  <div className="flex items-center mb-3">
                    <div className="flex items-center">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="text-sm text-gray-600 ml-1">{product.rating}</span>
                    </div>
                    <span className="text-sm text-gray-500 ml-2">({product.reviewCount} değerlendirme)</span>
                  </div>

                  <div className="flex items-center space-x-2 mb-4">
                    {product.colors.map((color, index) => (
                      <span key={index} className="text-xs bg-gray-100 px-2 py-1 rounded">
                        {color}
                      </span>
                    ))}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl font-bold text-[#B8860B]">{product.price}₺</span>
                      {product.originalPrice && (
                        <span className="text-lg text-gray-500 line-through">{product.originalPrice}₺</span>
                      )}
                    </div>
                    <button
                      disabled={!product.inStock}
                      onClick={() => product.inStock && addToCart(product)}
                      className={`font-semibold py-2 px-4 rounded-full transition-colors ${
                        product.inStock
                          ? 'bg-[#F4C2C2] hover:bg-[#E8A5A5] text-gray-900'
                          : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      }`}
                    >
                      {product.inStock ? 'Sepete Ekle' : 'Stokta Yok'}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
