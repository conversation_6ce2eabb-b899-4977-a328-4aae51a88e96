"use client";

import Image from "next/image";
import Link from "next/link";
import { ArrowLeft, Gift } from "lucide-react";

export default function KurulumVideosuPage() {
  return (
    <div className="min-h-screen bg-[#F4C2C2] flex flex-col items-center justify-center p-4 sm:p-6">
      <div className="w-full max-w-xl sm:max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-6">
          <div className="flex items-center justify-center mb-4">
            <span className="text-4xl mr-2">🎁</span>
            <h1 className="text-2xl sm:text-3xl md:text-4xl font-bold text-black">Hediye <PERSON>rün <PERSON>su</h1>
          </div>
        </div>

        {/* Video Container */}
        <div className="bg-white rounded-3xl shadow-2xl p-4 sm:p-6 md:p-8 mb-6">
          <div className="relative w-full max-w-lg mx-auto">
            <div className="bg-gradient-to-br from-[#B8860B]/10 to-[#B8860B]/20 rounded-2xl p-3 sm:p-4">
              <Image
                src="/assets/kurulum.gif"
                alt="Hediye Ürün Kurulum Videosu"
                width={600}
                height={400}
                className="w-full h-auto rounded-xl shadow-lg"
                unoptimized
                priority
              />
            </div>
          </div>

          <div className="text-center mt-4 sm:mt-6">
            <h2 className="text-lg sm:text-xl md:text-2xl font-bold text-gray-800 mb-2 sm:mb-3">
              🎯 Kolay Kurulum Adımları
            </h2>
            <p className="text-sm sm:text-base text-gray-600 leading-relaxed px-2">
              Hediye ürünlerinizi kolayca kurmak için yukarıdaki videoyu takip edin.
              Adım adım gösterilen talimatlarla ürününüzü hızlıca hazır hale getirebilirsiniz.
            </p>
          </div>
        </div>

        {/* Action Button */}
        <div className="flex justify-center">
          <Link
            href="/links"
            className="flex items-center justify-center bg-gradient-to-r from-[#B8860B] to-[#A0750A] hover:from-[#A0750A] hover:to-[#8B6508] text-white font-bold py-3 sm:py-4 px-6 sm:px-8 rounded-2xl transition-all duration-300 hover:scale-105 hover:shadow-xl border-2 border-white/20 text-sm sm:text-base"
          >
            <ArrowLeft className="w-4 h-4 sm:w-5 sm:h-5 mr-2" />
            Linkler Sayfasına Dön
          </Link>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-black/60 text-sm">
            © {new Date().getFullYear()} Lilys İstanbul - Hediye Ürün Kurulum Rehberi
          </p>
        </div>
      </div>
    </div>
  );
}
