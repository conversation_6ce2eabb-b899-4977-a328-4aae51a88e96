'use client';

import { useState } from 'react';
import Image from "next/image";
import Link from "next/link";
import { useCart } from "@/contexts/CartContext";
import { ArrowLeft, CreditCard, MapPin, User, Phone, Mail } from "lucide-react";

export default function CheckoutPage() {
  const { cart, getTotalAmount, getTotalItems, clearCart } = useCart();
  const [step, setStep] = useState(1); // 1: Adres, 2: Ödeme, 3: Onay
  const [isProcessing, setIsProcessing] = useState(false);

  const [shippingAddress, setShippingAddress] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    district: '',
    postalCode: ''
  });

  const [billingAddress, setBillingAddress] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    district: '',
    postalCode: ''
  });

  const [sameAsShipping, setSameAsShipping] = useState(true);

  if (cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2] flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Sepetiniz Boş</h1>
          <p className="text-gray-600 mb-6">Ödeme yapabilmek için sepetinizde ürün bulunmalıdır.</p>
          <Link
            href="/"
            className="inline-flex items-center bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-3 px-6 rounded-full transition-all duration-300"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            Alışverişe Devam Et
          </Link>
        </div>
      </div>
    );
  }

  const shippingCost = getTotalAmount() > 500 ? 0 : 29.90;
  const totalWithShipping = getTotalAmount() + shippingCost;

  const handleSubmitAddress = (e: React.FormEvent) => {
    e.preventDefault();
    setStep(2);
  };

  const handlePayment = async () => {
    setIsProcessing(true);
    
    // Simulated payment process
    setTimeout(() => {
      setIsProcessing(false);
      setStep(3);
      // clearCart(); // Gerçek uygulamada ödeme başarılı olduktan sonra sepeti temizle
    }, 3000);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2]">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-[#F4C2C2]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/assets/lilys-logo.jpg"
                alt="Lilys Istanbul"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="text-xl font-bold text-gray-900">Lilys İstanbul</span>
            </Link>
            
            <Link
              href="/cart"
              className="flex items-center text-[#B8860B] hover:text-[#A0750A] transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Sepete Dön
            </Link>
          </div>
        </div>
      </header>

      {/* Progress Steps */}
      <div className="bg-white/50 py-6">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center space-x-8">
            {[
              { number: 1, title: 'Adres Bilgileri', icon: MapPin },
              { number: 2, title: 'Ödeme', icon: CreditCard },
              { number: 3, title: 'Onay', icon: User }
            ].map((stepItem) => (
              <div key={stepItem.number} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  step >= stepItem.number 
                    ? 'bg-[#B8860B] border-[#B8860B] text-white' 
                    : 'border-gray-300 text-gray-500'
                }`}>
                  {step > stepItem.number ? (
                    <span className="text-sm">✓</span>
                  ) : (
                    <stepItem.icon className="w-5 h-5" />
                  )}
                </div>
                <span className={`ml-2 font-semibold ${
                  step >= stepItem.number ? 'text-[#B8860B]' : 'text-gray-500'
                }`}>
                  {stepItem.title}
                </span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {step === 1 && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Teslimat Adresi</h2>
                <form onSubmit={handleSubmitAddress} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Ad</label>
                      <input
                        type="text"
                        required
                        value={shippingAddress.firstName}
                        onChange={(e) => setShippingAddress({...shippingAddress, firstName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Soyad</label>
                      <input
                        type="text"
                        required
                        value={shippingAddress.lastName}
                        onChange={(e) => setShippingAddress({...shippingAddress, lastName: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">E-posta</label>
                      <input
                        type="email"
                        required
                        value={shippingAddress.email}
                        onChange={(e) => setShippingAddress({...shippingAddress, email: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Telefon</label>
                      <input
                        type="tel"
                        required
                        value={shippingAddress.phone}
                        onChange={(e) => setShippingAddress({...shippingAddress, phone: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">Adres</label>
                    <textarea
                      required
                      rows={3}
                      value={shippingAddress.address}
                      onChange={(e) => setShippingAddress({...shippingAddress, address: e.target.value})}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">İl</label>
                      <input
                        type="text"
                        required
                        value={shippingAddress.city}
                        onChange={(e) => setShippingAddress({...shippingAddress, city: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">İlçe</label>
                      <input
                        type="text"
                        required
                        value={shippingAddress.district}
                        onChange={(e) => setShippingAddress({...shippingAddress, district: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Posta Kodu</label>
                      <input
                        type="text"
                        required
                        value={shippingAddress.postalCode}
                        onChange={(e) => setShippingAddress({...shippingAddress, postalCode: e.target.value})}
                        className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#B8860B] focus:border-transparent"
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-4 px-6 rounded-full transition-all duration-300 hover:scale-105"
                  >
                    Ödemeye Geç
                  </button>
                </form>
              </div>
            )}

            {step === 2 && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
                <h2 className="text-2xl font-bold text-gray-900 mb-6">Ödeme Bilgileri</h2>
                <div className="space-y-6">
                  <div className="bg-[#F4C2C2]/30 p-6 rounded-xl">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">iyzico ile Güvenli Ödeme</h3>
                    <p className="text-gray-600 mb-4">
                      Ödemeniz iyzico güvencesi altında işleme alınacaktır. Kredi kartı bilgileriniz güvenli bir şekilde şifrelenir.
                    </p>
                    <div className="flex items-center space-x-4 mb-6">
                      <div className="bg-white p-2 rounded">
                        <CreditCard className="w-8 h-8 text-[#B8860B]" />
                      </div>
                      <span className="text-sm text-gray-600">Visa, Mastercard, American Express kabul edilir</span>
                    </div>
                    <button
                      onClick={handlePayment}
                      disabled={isProcessing}
                      className="w-full bg-[#B8860B] hover:bg-[#A0750A] disabled:bg-gray-400 text-white font-bold py-4 px-6 rounded-full transition-all duration-300 hover:scale-105 disabled:hover:scale-100"
                    >
                      {isProcessing ? 'Ödeme İşleniyor...' : `${totalWithShipping.toFixed(2)}₺ Öde`}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg text-center">
                <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                  <span className="text-3xl text-green-600">✓</span>
                </div>
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Siparişiniz Alındı!</h2>
                <p className="text-gray-600 mb-6">
                  Siparişiniz başarıyla oluşturuldu. Kısa süre içinde e-posta adresinize sipariş detayları gönderilecektir.
                </p>
                <div className="bg-[#F4C2C2]/30 p-4 rounded-lg mb-6">
                  <p className="text-sm text-gray-700">
                    <strong>Sipariş No:</strong> #LI{Date.now().toString().slice(-6)}
                  </p>
                </div>
                <Link
                  href="/"
                  className="inline-block bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-3 px-6 rounded-full transition-all duration-300 hover:scale-105"
                >
                  Alışverişe Devam Et
                </Link>
              </div>
            )}
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg sticky top-24">
              <h3 className="text-xl font-bold text-gray-900 mb-6">Sipariş Özeti</h3>
              
              <div className="space-y-4 mb-6">
                {cart.items.map((item) => (
                  <div key={`${item.productId}-${item.selectedColor}`} className="flex items-center space-x-3">
                    <Image
                      src={item.product.images?.[0] || '/assets/lilys-logo.jpg'}
                      alt={item.product.name}
                      width={50}
                      height={50}
                      className="rounded-lg object-cover"
                    />
                    <div className="flex-1">
                      <h4 className="font-semibold text-sm text-gray-900">{item.product.name}</h4>
                      {item.selectedColor && (
                        <p className="text-xs text-gray-600">Renk: {item.selectedColor}</p>
                      )}
                      <p className="text-xs text-gray-600">Adet: {item.quantity}</p>
                    </div>
                    <span className="font-semibold text-[#B8860B]">
                      {(item.product.price * item.quantity).toFixed(2)}₺
                    </span>
                  </div>
                ))}
              </div>

              <div className="space-y-3 mb-6 pt-4 border-t border-gray-200">
                <div className="flex justify-between">
                  <span className="text-gray-600">Ara Toplam</span>
                  <span className="font-semibold">{getTotalAmount().toFixed(2)}₺</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Kargo</span>
                  <span className="font-semibold">
                    {shippingCost === 0 ? 'Ücretsiz' : `${shippingCost.toFixed(2)}₺`}
                  </span>
                </div>
                
                <hr className="border-gray-200" />
                
                <div className="flex justify-between text-lg font-bold">
                  <span>Toplam</span>
                  <span className="text-[#B8860B]">{totalWithShipping.toFixed(2)}₺</span>
                </div>
              </div>

              {shippingCost > 0 && (
                <div className="text-sm text-[#B8860B] bg-[#F4C2C2]/30 p-3 rounded-lg">
                  500₺ ve üzeri alışverişlerde kargo ücretsiz!
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
