'use client';

import { use, useState } from 'react';
import Image from "next/image";
import Link from "next/link";
import { useCart } from "@/contexts/CartContext";
import { Star, Heart, ShoppingBag, Minus, Plus, Truck, Shield, RotateCcw } from "lucide-react";
import { Metadata } from 'next';

interface ProductPageProps {
  params: Promise<{ id: string }>;
}

export default function ProductPage({ params }: ProductPageProps) {
  const { id } = use(params);
  const { addToCart, getTotalItems } = useCart();
  const [selectedColor, setSelectedColor] = useState<string>('');
  const [quantity, setQuantity] = useState(1);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Mock product data - gerçek veriler Firebase'den gelecek
  const product = {
    id: id,
    name: 'Premium Seyahat Organizatörü Seti',
    description: 'Valizinizi düzenli tutmak için mükemmel çözüm. Su geçirmez malzemeden üretilmiş, dayanıklı ve pratik tasarım.',
    price: 299,
    originalPrice: 399,
    images: [
      '/assets/lilys-logo.jpg',
      '/assets/lilys-logo.jpg',
      '/assets/lilys-logo.jpg'
    ],
    category: 'seyahat' as const,
    inStock: true,
    stockQuantity: 15,
    features: [
      'Su geçirmez malzeme',
      'Çok bölmeli tasarım',
      'Dayanıklı fermuarlar',
      'Kompakt boyut',
      'Kolay temizlenir'
    ],
    dimensions: {
      width: 30,
      height: 20,
      depth: 10,
      weight: 0.5
    },
    materials: ['Premium Plastik', 'Su Geçirmez Kumaş'],
    colors: ['Pembe', 'Mavi', 'Gri', 'Siyah'],
    tags: ['seyahat', 'organizatör', 'valiz'],
    rating: 4.8,
    reviewCount: 124,
    createdAt: new Date(),
    updatedAt: new Date(),
    isActive: true,
    isFeatured: true
  };

  const reviews = [
    {
      id: '1',
      userName: 'Ayşe K.',
      rating: 5,
      title: 'Mükemmel ürün!',
      comment: 'Seyahatlerimde çok işime yaradı. Kaliteli malzeme ve pratik tasarım.',
      createdAt: new Date('2024-01-15'),
      isVerifiedPurchase: true
    },
    {
      id: '2',
      userName: 'Fatma M.',
      rating: 4,
      title: 'Çok kullanışlı',
      comment: 'Valizimi çok daha düzenli tutabiliyorum. Tavsiye ederim.',
      createdAt: new Date('2024-01-10'),
      isVerifiedPurchase: true
    }
  ];

  const handleAddToCart = () => {
    addToCart(product, quantity, selectedColor);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2]">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-[#F4C2C2]/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/assets/lilys-logo.jpg"
                alt="Lilys Istanbul"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="text-xl font-bold text-gray-900">Lilys İstanbul</span>
            </Link>
            
            <nav className="hidden md:flex items-center space-x-8">
              <Link href="/" className="text-[#B8860B] font-semibold">Ana Sayfa</Link>
              <Link href="/links" className="text-gray-700 hover:text-[#B8860B] transition-colors">Linkler</Link>
            </nav>

            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-700 hover:text-[#B8860B] transition-colors">
                <Heart className="w-6 h-6" />
              </button>
              <Link href="/cart" className="p-2 text-gray-700 hover:text-[#B8860B] transition-colors relative">
                <ShoppingBag className="w-6 h-6" />
                <span className="absolute -top-1 -right-1 bg-[#B8860B] text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                  {getTotalItems()}
                </span>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Breadcrumb */}
      <div className="bg-white/50 py-4 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center space-x-2 text-sm">
            <Link href="/" className="text-[#B8860B] hover:underline">Ana Sayfa</Link>
            <span className="text-gray-500">/</span>
            <Link href={`/category/${product.category}`} className="text-[#B8860B] hover:underline">
              Seyahat Ürünleri
            </Link>
            <span className="text-gray-500">/</span>
            <span className="text-gray-900 font-semibold">{product.name}</span>
          </div>
        </div>
      </div>

      {/* Product Details */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div className="space-y-4">
            <div className="aspect-square bg-white rounded-2xl overflow-hidden shadow-lg">
              <Image
                src={product.images[selectedImageIndex]}
                alt={product.name}
                width={600}
                height={600}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="flex space-x-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImageIndex(index)}
                  className={`w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImageIndex === index ? 'border-[#B8860B]' : 'border-gray-200'
                  }`}
                >
                  <Image
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{product.name}</h1>
              <div className="flex items-center space-x-4 mb-4">
                <div className="flex items-center">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-5 h-5 ${
                        i < Math.floor(product.rating) ? 'text-yellow-400 fill-current' : 'text-gray-300'
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm text-gray-600">
                    {product.rating} ({product.reviewCount} değerlendirme)
                  </span>
                </div>
              </div>
              <p className="text-gray-600 text-lg leading-relaxed">{product.description}</p>
            </div>

            {/* Price */}
            <div className="flex items-center space-x-4">
              <span className="text-4xl font-bold text-[#B8860B]">{product.price}₺</span>
              {product.originalPrice && (
                <span className="text-2xl text-gray-500 line-through">{product.originalPrice}₺</span>
              )}
              {product.originalPrice && (
                <span className="bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm font-semibold">
                  %{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)} İndirim
                </span>
              )}
            </div>

            {/* Colors */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Renk Seçin</h3>
              <div className="flex space-x-3">
                {product.colors.map((color) => (
                  <button
                    key={color}
                    onClick={() => setSelectedColor(color)}
                    className={`px-4 py-2 rounded-full border-2 transition-colors ${
                      selectedColor === color
                        ? 'border-[#B8860B] bg-[#B8860B] text-white'
                        : 'border-gray-300 bg-white text-gray-700 hover:border-[#B8860B]'
                    }`}
                  >
                    {color}
                  </button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Adet</h3>
              <div className="flex items-center space-x-4">
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="p-2 hover:bg-gray-100 transition-colors"
                  >
                    <Minus className="w-4 h-4" />
                  </button>
                  <span className="px-4 py-2 font-semibold">{quantity}</span>
                  <button
                    onClick={() => setQuantity(Math.min(product.stockQuantity, quantity + 1))}
                    className="p-2 hover:bg-gray-100 transition-colors"
                  >
                    <Plus className="w-4 h-4" />
                  </button>
                </div>
                <span className="text-sm text-gray-600">
                  Stokta {product.stockQuantity} adet
                </span>
              </div>
            </div>

            {/* Add to Cart */}
            <div className="space-y-4">
              <button
                onClick={handleAddToCart}
                disabled={!product.inStock}
                className="w-full bg-[#B8860B] hover:bg-[#A0750A] disabled:bg-gray-300 disabled:cursor-not-allowed text-white font-bold py-4 px-8 rounded-full text-lg transition-all duration-300 hover:scale-105 shadow-lg"
              >
                {product.inStock ? 'Sepete Ekle' : 'Stokta Yok'}
              </button>
              
              <button className="w-full bg-white hover:bg-gray-50 text-[#B8860B] font-bold py-4 px-8 rounded-full text-lg border-2 border-[#B8860B] transition-all duration-300 hover:scale-105">
                Favorilere Ekle
              </button>
            </div>

            {/* Features */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-200">
              <div className="flex items-center space-x-3">
                <Truck className="w-6 h-6 text-[#B8860B]" />
                <div>
                  <p className="font-semibold text-gray-900">Hızlı Kargo</p>
                  <p className="text-sm text-gray-600">1-3 iş günü</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Shield className="w-6 h-6 text-[#B8860B]" />
                <div>
                  <p className="font-semibold text-gray-900">Güvenli Ödeme</p>
                  <p className="text-sm text-gray-600">SSL korumalı</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <RotateCcw className="w-6 h-6 text-[#B8860B]" />
                <div>
                  <p className="font-semibold text-gray-900">Kolay İade</p>
                  <p className="text-sm text-gray-600">14 gün içinde</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Features */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Özellikler</h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <span className="w-2 h-2 bg-[#B8860B] rounded-full"></span>
                      <span className="text-gray-700">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Specifications */}
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4">Teknik Özellikler</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Boyutlar:</span>
                    <span className="font-semibold">
                      {product.dimensions.width} x {product.dimensions.height} x {product.dimensions.depth} cm
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Ağırlık:</span>
                    <span className="font-semibold">{product.dimensions.weight} kg</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Malzeme:</span>
                    <span className="font-semibold">{product.materials.join(', ')}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Reviews */}
        <div className="mt-16">
          <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 mb-6">Müşteri Yorumları</h3>
            <div className="space-y-6">
              {reviews.map((review) => (
                <div key={review.id} className="border-b border-gray-200 pb-6 last:border-b-0">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="font-semibold text-gray-900">{review.userName}</span>
                      {review.isVerifiedPurchase && (
                        <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-semibold">
                          Doğrulanmış Alışveriş
                        </span>
                      )}
                    </div>
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star
                          key={i}
                          className={`w-4 h-4 ${
                            i < review.rating ? 'text-yellow-400 fill-current' : 'text-gray-300'
                          }`}
                        />
                      ))}
                    </div>
                  </div>
                  <h4 className="font-semibold text-gray-900 mb-2">{review.title}</h4>
                  <p className="text-gray-600 mb-2">{review.comment}</p>
                  <p className="text-sm text-gray-500">
                    {review.createdAt.toLocaleDateString('tr-TR')}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
