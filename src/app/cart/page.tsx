'use client';

import Image from "next/image";
import Link from "next/link";
import { useCart } from "@/contexts/CartContext";
import { Minus, Plus, Trash2, ShoppingBag, ArrowLeft } from "lucide-react";

export default function CartPage() {
  const { cart, updateQuantity, removeFromCart, clearCart, getTotalAmount, getTotalItems } = useCart();

  if (cart.items.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2]">
        {/* Header */}
        <header className="bg-white/80 backdrop-blur-md border-b border-[#F4C2C2]/20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex items-center justify-between h-16">
              <Link href="/" className="flex items-center space-x-3">
                <Image
                  src="/assets/lilys-logo.jpg"
                  alt="Lilys Istanbul"
                  width={40}
                  height={40}
                  className="rounded-full"
                />
                <span className="text-xl font-bold text-gray-900">Lilys İstanbul</span>
              </Link>
            </div>
          </div>
        </header>

        {/* Empty Cart */}
        <div className="flex flex-col items-center justify-center min-h-[80vh] px-4">
          <div className="text-center">
            <ShoppingBag className="w-24 h-24 text-gray-300 mx-auto mb-6" />
            <h1 className="text-3xl font-bold text-gray-900 mb-4">Sepetiniz Boş</h1>
            <p className="text-gray-600 mb-8 max-w-md">
              Henüz sepetinize ürün eklemediniz. Harika ürünlerimizi keşfetmek için alışverişe başlayın!
            </p>
            <Link
              href="/"
              className="inline-flex items-center bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-3 px-6 rounded-full transition-all duration-300 hover:scale-105"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              Alışverişe Devam Et
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const shippingCost = getTotalAmount() > 500 ? 0 : 29.90;
  const totalWithShipping = getTotalAmount() + shippingCost;

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F4C2C2] via-[#F8F9FA] to-[#F4C2C2]">
      {/* Header */}
      <header className="bg-white/80 backdrop-blur-md border-b border-[#F4C2C2]/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <Link href="/" className="flex items-center space-x-3">
              <Image
                src="/assets/lilys-logo.jpg"
                alt="Lilys Istanbul"
                width={40}
                height={40}
                className="rounded-full"
              />
              <span className="text-xl font-bold text-gray-900">Lilys İstanbul</span>
            </Link>
            
            <Link
              href="/"
              className="flex items-center text-[#B8860B] hover:text-[#A0750A] transition-colors"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Alışverişe Devam Et
            </Link>
          </div>
        </div>
      </header>

      {/* Cart Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Cart Items */}
          <div className="lg:col-span-2">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg">
              <div className="flex items-center justify-between mb-6">
                <h1 className="text-2xl font-bold text-gray-900">
                  Sepetim ({getTotalItems()} ürün)
                </h1>
                <button
                  onClick={clearCart}
                  className="text-red-500 hover:text-red-700 text-sm font-semibold transition-colors"
                >
                  Sepeti Temizle
                </button>
              </div>

              <div className="space-y-4">
                {cart.items.map((item) => (
                  <div
                    key={`${item.productId}-${item.selectedColor || 'default'}`}
                    className="flex items-center space-x-4 p-4 bg-white rounded-xl border border-gray-100"
                  >
                    <Image
                      src={item.product.images?.[0] || '/assets/lilys-logo.jpg'}
                      alt={item.product.name}
                      width={80}
                      height={80}
                      className="rounded-lg object-cover"
                    />
                    
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{item.product.name}</h3>
                      {item.selectedColor && (
                        <p className="text-sm text-gray-600">Renk: {item.selectedColor}</p>
                      )}
                      <p className="text-lg font-bold text-[#B8860B]">{item.product.price}₺</p>
                    </div>

                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => updateQuantity(item.productId, item.quantity - 1)}
                        className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                      >
                        <Minus className="w-4 h-4" />
                      </button>
                      
                      <span className="w-8 text-center font-semibold">{item.quantity}</span>
                      
                      <button
                        onClick={() => updateQuantity(item.productId, item.quantity + 1)}
                        className="p-1 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors"
                      >
                        <Plus className="w-4 h-4" />
                      </button>
                    </div>

                    <div className="text-right">
                      <p className="font-bold text-gray-900">
                        {(item.product.price * item.quantity).toFixed(2)}₺
                      </p>
                      <button
                        onClick={() => removeFromCart(item.productId)}
                        className="text-red-500 hover:text-red-700 mt-2 transition-colors"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg sticky top-24">
              <h2 className="text-xl font-bold text-gray-900 mb-6">Sipariş Özeti</h2>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between">
                  <span className="text-gray-600">Ara Toplam</span>
                  <span className="font-semibold">{getTotalAmount().toFixed(2)}₺</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Kargo</span>
                  <span className="font-semibold">
                    {shippingCost === 0 ? 'Ücretsiz' : `${shippingCost.toFixed(2)}₺`}
                  </span>
                </div>
                
                {shippingCost > 0 && (
                  <div className="text-sm text-[#B8860B] bg-[#F4C2C2]/30 p-3 rounded-lg">
                    500₺ ve üzeri alışverişlerde kargo ücretsiz!
                  </div>
                )}
                
                <hr className="border-gray-200" />
                
                <div className="flex justify-between text-lg font-bold">
                  <span>Toplam</span>
                  <span className="text-[#B8860B]">{totalWithShipping.toFixed(2)}₺</span>
                </div>
              </div>

              <Link
                href="/checkout"
                className="w-full bg-[#B8860B] hover:bg-[#A0750A] text-white font-bold py-4 px-6 rounded-full text-center block transition-all duration-300 hover:scale-105 shadow-lg"
              >
                Ödemeye Geç
              </Link>
              
              <p className="text-xs text-gray-500 text-center mt-4">
                Güvenli ödeme ile korumalı alışveriş
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
