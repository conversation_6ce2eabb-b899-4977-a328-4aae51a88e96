// Product Types
export interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  images: string[];
  category: ProductCategory;
  subcategory?: string;
  inStock: boolean;
  stockQuantity: number;
  features: string[];
  dimensions?: {
    width: number;
    height: number;
    depth: number;
    weight: number;
  };
  materials: string[];
  colors: string[];
  tags: string[];
  rating: number;
  reviewCount: number;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  isFeatured: boolean;
}

// Product Categories
export type ProductCategory = 'seyahat' | 'ev' | 'duzen';

export interface Category {
  id: ProductCategory;
  name: string;
  description: string;
  icon: string;
  subcategories: Subcategory[];
}

export interface Subcategory {
  id: string;
  name: string;
  description: string;
}

// Cart Types
export interface CartItem {
  productId: string;
  product: Product;
  quantity: number;
  selectedColor?: string;
  addedAt: Date;
}

export interface Cart {
  id: string;
  userId?: string;
  items: CartItem[];
  totalAmount: number;
  totalItems: number;
  createdAt: Date;
  updatedAt: Date;
}

// Order Types
export interface Order {
  id: string;
  userId: string;
  items: CartItem[];
  shippingAddress: Address;
  billingAddress: Address;
  totalAmount: number;
  shippingCost: number;
  taxAmount: number;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  paymentMethod: 'iyzico';
  paymentId?: string;
  trackingNumber?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  estimatedDelivery?: Date;
}

export type OrderStatus = 'pending' | 'confirmed' | 'preparing' | 'shipped' | 'delivered' | 'cancelled';
export type PaymentStatus = 'pending' | 'paid' | 'failed' | 'refunded';

// User Types
export interface User {
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  addresses: Address[];
  orders: string[];
  wishlist: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Address {
  id: string;
  title: string;
  firstName: string;
  lastName: string;
  company?: string;
  address: string;
  city: string;
  district: string;
  postalCode: string;
  country: string;
  phone: string;
  isDefault: boolean;
}

// Review Types
export interface Review {
  id: string;
  productId: string;
  userId: string;
  userName: string;
  rating: number;
  title: string;
  comment: string;
  images?: string[];
  isVerifiedPurchase: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Filter Types
export interface ProductFilters {
  category?: ProductCategory;
  subcategory?: string;
  priceRange?: {
    min: number;
    max: number;
  };
  colors?: string[];
  materials?: string[];
  inStock?: boolean;
  rating?: number;
  sortBy?: 'price-asc' | 'price-desc' | 'name-asc' | 'name-desc' | 'rating' | 'newest';
}
