// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAnalytics } from "firebase/analytics";
import { getAuth } from "firebase/auth";
import { getFirestore } from "firebase/firestore";
import { getStorage } from "firebase/storage";

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDg-3Unrk4NvG4azGFNOl7drSUjze2ww4k",
  authDomain: "lilys-6d22f.firebaseapp.com",
  projectId: "lilys-6d22f",
  storageBucket: "lilys-6d22f.firebasestorage.app",
  messagingSenderId: "328022006995",
  appId: "1:328022006995:web:7b3ffd1988843aad6b8826",
  measurementId: "G-SXG3SKEH1Y"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);

// Initialize Firebase services
export const auth = getAuth(app);
export const db = getFirestore(app);
export const storage = getStorage(app);

// Initialize Analytics (only in browser)
export const analytics = typeof window !== 'undefined' ? getAnalytics(app) : null;

export default app;
