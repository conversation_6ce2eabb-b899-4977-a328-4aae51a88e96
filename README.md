# Lilys İstanbul E-ticaret Sitesi

Kadınlara özel seyahat, ev ve düzen ürünleri satan modern e-ticaret sitesi.

## 🚀 Özellikler

- **Modern Tasarım**: 2025 standartlarında responsive tasarım
- **E-ticaret Fonksiyonları**: Sepet, ödeme, ürün yönetimi
- **Firebase Entegrasyonu**: Gerçek zamanlı veritabanı ve kimlik doğrulama
- **Admin Paneli**: Ürün ve sipariş yönetimi
- **SEO Optimizasyonu**: Arama motoru dostu yapı
- **Mobil Uyumlu**: Tüm cihazlarda mükemmel görünüm

## 🛠️ Teknolojiler

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS 4
- **Backend**: Firebase (Firestore, Auth, Storage)
- **Ödeme**: iyzico entegrasyonu
- **Icons**: Lucide React

## 📦 Kurulum

1. Projeyi klonlayın:
```bash
git clone https://github.com/miktadtahir/lilys.git
cd lilys
```

2. Bağımlılıkları yükleyin:
```bash
npm install
```

3. Firebase konfigürasyonunu ayarlayın:
   - `src/lib/firebase.ts` dosyasındaki konfigürasyonu kontrol edin
   - Firebase Console'dan gerekli servisleri aktifleştirin

4. Geliştirme sunucusunu başlatın:
```bash
npm run dev
```

5. Tarayıcınızda [http://localhost:3000](http://localhost:3000) adresini açın

## 📁 Proje Yapısı

```
src/
├── app/                    # Next.js App Router sayfaları
│   ├── admin/             # Admin paneli
│   ├── cart/              # Sepet sayfası
│   ├── checkout/          # Ödeme sayfası
│   ├── links/             # Sosyal medya linkleri
│   └── shop/              # E-ticaret sayfaları
├── components/            # Yeniden kullanılabilir bileşenler
├── contexts/              # React Context'leri
├── data/                  # Statik veriler
├── lib/                   # Yardımcı fonksiyonlar
└── types/                 # TypeScript tip tanımları
```

## 🎨 Tasarım Sistemi

### Renk Paleti
- **Ana Renk**: `#F4C2C2` (Soft Pink)
- **Destekleyici**: `#E8A5A5` (Darker Pink)
- **Accent**: `#B8860B` (Gold)
- **Nötr**: `#F8F9FA`, `#6C757D`

### Ürün Kategorileri
- 🧳 **Seyahat Ürünleri**: Seyahat aksesuarları, pasaport kutusu, sabun kutusu
- 🏠 **Ev Ürünleri**: Banyo ve mutfak aksesuarları
- 📋 **Düzen Ürünleri**: Düzenleyici kutular ve organizatörler

## 🔧 Geliştirme

### Yeni Ürün Ekleme
1. Firebase Console'da Firestore'a ürün verilerini ekleyin
2. Ürün görsellerini Firebase Storage'a yükleyin
3. Admin panelinden ürünleri yönetin

### Ödeme Entegrasyonu
- iyzico test/production anahtarlarını environment variables olarak ayarlayın
- Webhook URL'lerini iyzico panelinde yapılandırın

## 📱 Sayfalar

- `/` - Ana sayfa
- `/shop` - E-ticaret ana sayfası
- `/shop/category/[slug]` - Kategori sayfaları
- `/shop/product/[id]` - Ürün detay sayfaları
- `/cart` - Sepet
- `/checkout` - Ödeme
- `/links` - Sosyal medya linkleri (korunmuş)
- `/admin` - Admin paneli

## 🚀 Deployment

### Vercel (Önerilen)
1. GitHub'a push yapın
2. Vercel'e bağlayın
3. Environment variables'ları ayarlayın
4. Deploy edin

### Diğer Platformlar
- Netlify
- Firebase Hosting
- AWS Amplify

## 📈 SEO

- Sitemap otomatik oluşturulur (`/sitemap.xml`)
- Robots.txt yapılandırılmış (`/robots.txt`)
- Meta tags optimize edilmiş
- Structured data hazır

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📞 İletişim

- **Website**: [lilys-istanbul.com](https://lilys-istanbul.com)
- **Instagram**: [@lilysistanbul](https://instagram.com/lilysistanbul)
- **WhatsApp**: +90 533 669 54 59
